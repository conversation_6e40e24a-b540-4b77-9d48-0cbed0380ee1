<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Codem</title>
    <link rel="stylesheet" href="style.css">
    
    <script src="nui://game/ui/jquery.js" type="text/javascript"></script>
    <script src="https://code.jquery.com/jquery-3.5.0.js"></script>


    
  
</head>
<body>
  

    <div class="container" style="display:none">
      <div class="backgorund-shadow"></div>
        <div class="background-image">
          <img src="./images/bg.png" alt="">
        </div>
        <div class="cargaragetext">
          <img src="./images/garagetext.png" alt="">
        </div>
        <div class="carspecitext">
          <img src="./images/spesi.png" alt="">
        </div>
        <div style="display:none" class="notify">TEST Notify!</div>
        <div class="fuelbar">
          <img src="./images/fueltank.png" alt="">
          <h1>GASOLINE IN CAR</h1>
          <h2 class="fuellevel"></h2>

        </div>
        <div class="priceyes" style="display:none">
          <h4>ARE YOU SURE ?</h4>
          <h2 class="pricebutton" >YES</h2>
          <h3 class="priceyesbutton">NO</h3>  
          <img src="./images/yesno.png" alt="">
        </div>
        <div class="transferyes" style="display:none">
          <h4>ARE YOU SURE ?</h4>
          <h2>YES</h2>
          <h3 class="transferyescar">NO</h3>  
          <img src="./images/yesno.png" alt="">
        </div>
        <div class="sellprice">
          <h2>PRICE FOR CAR</h2>
          <h3 class="vehicleprice"></h3>
          <h4 class="sellpricecar">SELL</h4>
          <img src="./images/sellprice.png" alt="">
        </div> 
     
        <div class="transfer">
          <h2>TRANSFER TO ID</h2>
          <input id="transfer" type="number" placeholder="ID">
          <h3 class="transfertovehicle">TRANSFER</h3>
          <img src="./images/transfer.png" alt="">
        </div> 
        <div class="platepng">
          <h1>NUMBER PLATE</h1>
          <h2 class="platevehicle"></h2>
          <img src="./images/plate.png" alt="">
        </div>
        <div class="enginepng">
          <h1>ENGINE HEALTH</h1>
          <h2 class="damagevehicle"></h2>
          <img src="./images/engine.png" alt="">
        </div>
        <div class="windowpng">
          <h1>BODY HEALTH</h1>
          <h2 class="bodyhealth"></h2>
          <img src="./images/window.png" alt="">
        </div>
        <div class="parkpng">
          <h1>CAR STATUS</h1>
          <h2 class="stored"></h2>
          <img class="vehiclestoredpng" src="./images/park.png" alt="">
        </div>
        <div class="spawncar">
          <div id="spawncar">Spawn Car</div>
            <img src="./images/spawnbutton.png" alt="">
         
        </div>
     
        <div class="searchbox">
          <input id="searchvehicle" type="text" name="" id=""  placeholder="Seacrh car by carname">
       
        </div>
        <div class="closebutton">
          <h2>EXIT FROM GARAGE</h2>
          <img class="closeimg" src="./images/close.png" alt="">
        </div>

        

        <div class="selectedvehicle">
<!--  
           <div class="vehiclemodel">
            <div class="vehicleimage">
              <img src="./images/car.png" alt="">
            </div>
               

                <h2>Mitsubishi</h2>
                <h3>Lance Evolution X</h3>
                <div class="spawnselectcar"> Select Car</div>
            
          </div>  -->
          
       

        </div>

    </div>




  <script src="script.js"></script>
      
</body>
</html>